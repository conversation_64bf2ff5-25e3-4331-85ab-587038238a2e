{"rustc": 16591470773350601817, "features": "[\"compression\", \"custom-protocol\"]", "declared_features": "[\"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"isolation\", \"tracing\"]", "target": 4649449654257170297, "profile": 1369601567987815722, "path": 12343482105636496616, "deps": [[2671782512663819132, "tauri_utils", false, 2389448847489612802], [3060637413840920116, "proc_macro2", false, 891366040531786399], [13077543566650298139, "heck", false, 14315915692307211046], [14455244907590647360, "tauri_codegen", false, 3951396728455301775], [17990358020177143287, "quote", false, 7170081911087925092], [18149961000318489080, "syn", false, 7659822616811023533]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-macros-cbd5efaab7ba8c87\\dep-lib-tauri_macros", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}