{"rustc": 16591470773350601817, "features": "[\"brotli\", \"compression\", \"resources\", \"walkdir\"]", "declared_features": "[\"aes-gcm\", \"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"config-json5\", \"config-toml\", \"getrandom\", \"html-manipulation\", \"isolation\", \"json5\", \"proc-macro2\", \"process-relaunch-dangerous-allow-symlink-macos\", \"quote\", \"resources\", \"schema\", \"schemars\", \"serialize-to-javascript\", \"swift-rs\", \"walkdir\"]", "target": 7530130812022395703, "profile": 2040997289075261528, "path": 16521179712742780345, "deps": [[1200537532907108615, "url<PERSON><PERSON>n", false, 5426483048459580631], [3129130049864710036, "memchr", false, 6324576861998330843], [3150220818285335163, "url", false, 144294874758154728], [3191507132440681679, "serde_untagged", false, 17838675949747838404], [4899080583175475170, "semver", false, 8629626086987342457], [5986029879202738730, "log", false, 5740330685498469161], [6213549728662707793, "serde_with", false, 2268193653417570479], [6606131838865521726, "ctor", false, 4837961135221239191], [7170110829644101142, "json_patch", false, 1924833239260496720], [8319709847752024821, "uuid", false, 6592169030374714414], [9010263965687315507, "http", false, 17766510177696197715], [9451456094439810778, "regex", false, 8090463942343281495], [9556762810601084293, "brotli", false, 693153130375967707], [9689903380558560274, "serde", false, 12580537829973814340], [10806645703491011684, "thiserror", false, 16536509331773121597], [11989259058781683633, "dunce", false, 3077557957928947652], [13625485746686963219, "anyhow", false, 7663442346581735029], [15367738274754116744, "serde_json", false, 2971026785735191564], [15609422047640926750, "toml", false, 14711470672073032512], [15622660310229662834, "walkdir", false, 12240473425763297538], [17146114186171651583, "infer", false, 12545807206995070214], [17155886227862585100, "glob", false, 6666768574698413362], [17186037756130803222, "phf", false, 15525930522957564099]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-utils-ec21576b2c5170d1\\dep-lib-tauri_utils", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}