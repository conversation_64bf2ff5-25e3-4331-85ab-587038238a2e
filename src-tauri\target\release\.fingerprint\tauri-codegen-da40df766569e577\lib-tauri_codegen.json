{"rustc": 16591470773350601817, "features": "[\"brotli\", \"compression\"]", "declared_features": "[\"brotli\", \"compression\", \"config-json5\", \"config-toml\", \"isolation\"]", "target": 17460618180909919773, "profile": 1369601567987815722, "path": 18355538356694856097, "deps": [[2671782512663819132, "tauri_utils", false, 2389448847489612802], [3060637413840920116, "proc_macro2", false, 891366040531786399], [3150220818285335163, "url", false, 2817011047027935690], [4899080583175475170, "semver", false, 479068139954186366], [7170110829644101142, "json_patch", false, 13428550861027558761], [7392050791754369441, "ico", false, 18435758082074308759], [8319709847752024821, "uuid", false, 5835487573760639097], [9556762810601084293, "brotli", false, 8359474311949613760], [9689903380558560274, "serde", false, 11826711578205325068], [9857275760291862238, "sha2", false, 1024957776225741812], [10806645703491011684, "thiserror", false, 17237118037303288792], [12687914511023397207, "png", false, 11399967910644991418], [13077212702700853852, "base64", false, 6750175450531207907], [15367738274754116744, "serde_json", false, 9804563810918483774], [15622660310229662834, "walkdir", false, 15190750716767943960], [17990358020177143287, "quote", false, 7170081911087925092], [18149961000318489080, "syn", false, 7659822616811023533]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-codegen-da40df766569e577\\dep-lib-tauri_codegen", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}