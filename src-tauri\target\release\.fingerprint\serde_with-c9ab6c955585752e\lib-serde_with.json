{"rustc": 16591470773350601817, "features": "[\"alloc\", \"default\", \"macros\", \"std\"]", "declared_features": "[\"alloc\", \"base64\", \"chrono\", \"chrono_0_4\", \"default\", \"guide\", \"hashbrown_0_14\", \"hashbrown_0_15\", \"hex\", \"indexmap\", \"indexmap_1\", \"indexmap_2\", \"json\", \"macros\", \"schemars_0_8\", \"std\", \"time_0_3\"]", "target": 10448421281463538527, "profile": 722405520834827680, "path": 115447198856752076, "deps": [[6158493786865284961, "serde_with_macros", false, 2410449236599005520], [9689903380558560274, "serde", false, 11826711578205325068], [16257276029081467297, "serde_derive", false, 6316481539230272041]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\serde_with-c9ab6c955585752e\\dep-lib-serde_with", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}