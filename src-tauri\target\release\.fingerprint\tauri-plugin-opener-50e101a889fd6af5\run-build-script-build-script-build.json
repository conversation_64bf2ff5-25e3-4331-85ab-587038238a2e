{"rustc": 16591470773350601817, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[14039947826026167952, "build_script_build", false, 2776034111508375827], [3935545708480822364, "build_script_build", false, 16628602775902829200]], "local": [{"RerunIfChanged": {"output": "release\\build\\tauri-plugin-opener-50e101a889fd6af5\\output", "paths": ["permissions"]}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}