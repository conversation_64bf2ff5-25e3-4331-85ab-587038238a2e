{"rustc": 16591470773350601817, "features": "[]", "declared_features": "[\"devtools\", \"macos-private-api\"]", "target": 10306386172444932100, "profile": 2040997289075261528, "path": 11398684994473232277, "deps": [[2671782512663819132, "tauri_utils", false, 8049996397165116033], [3150220818285335163, "url", false, 144294874758154728], [4143744114649553716, "raw_window_handle", false, 6294644525462411982], [6089812615193535349, "build_script_build", false, 8043735642684842944], [7606335748176206944, "dpi", false, 9032923922779395649], [9010263965687315507, "http", false, 17766510177696197715], [9689903380558560274, "serde", false, 12580537829973814340], [10806645703491011684, "thiserror", false, 16536509331773121597], [13116089016666501665, "windows", false, 8496220921591022370], [15367738274754116744, "serde_json", false, 2971026785735191564], [16727543399706004146, "cookie", false, 810650797183716647]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-runtime-d1e1eea47cf2b28c\\dep-lib-tauri_runtime", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}