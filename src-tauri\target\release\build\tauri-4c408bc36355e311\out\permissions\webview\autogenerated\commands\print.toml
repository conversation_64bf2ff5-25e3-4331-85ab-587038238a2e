# Copyright 2019-2024 Tauri Programme within The Commons Conservancy
# SPDX-License-Identifier: Apache-2.0
# SPDX-License-Identifier: MIT
# Automatically generated - DO NOT EDIT!

[[permission]]
identifier = "allow-print"
description = "Enables the print command without any pre-configured scope."
commands.allow = ["print"]

[[permission]]
identifier = "deny-print"
description = "Denies the print command without any pre-configured scope."
commands.deny = ["print"]
