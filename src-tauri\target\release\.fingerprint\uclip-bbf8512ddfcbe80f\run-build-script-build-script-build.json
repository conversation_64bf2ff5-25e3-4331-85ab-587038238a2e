{"rustc": 16591470773350601817, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[14039947826026167952, "build_script_build", false, 2776034111508375827], [2979645065415301498, "build_script_build", false, 10697525462657267088], [3834743577069889284, "build_script_build", false, 8837905062299890667], [13890802266741835355, "build_script_build", false, 7910732267726307152], [3935545708480822364, "build_script_build", false, 3253837574187599486], [16934220019573174942, "build_script_build", false, 13259523445494559818]], "local": [{"RerunIfChanged": {"output": "release\\build\\uclip-bbf8512ddfcbe80f\\output", "paths": ["tauri.conf.json", "capabilities"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}