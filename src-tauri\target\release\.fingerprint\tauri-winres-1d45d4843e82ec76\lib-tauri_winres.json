{"rustc": 16591470773350601817, "features": "[]", "declared_features": "[]", "target": 2086567024422996381, "profile": 1369601567987815722, "path": 1291411732872411484, "deps": [[9934981346417206459, "embed_resource", false, 6000228093811261036], [14483812548788871374, "indexmap", false, 9570227610413557374], [15609422047640926750, "toml", false, 5501034043401252101]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-winres-1d45d4843e82ec76\\dep-lib-tauri_winres", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}