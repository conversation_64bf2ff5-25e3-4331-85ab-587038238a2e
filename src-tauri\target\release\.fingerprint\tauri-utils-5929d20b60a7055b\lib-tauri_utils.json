{"rustc": 16591470773350601817, "features": "[\"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"html-manipulation\", \"proc-macro2\", \"quote\", \"resources\", \"schema\", \"schemars\", \"swift-rs\", \"walkdir\"]", "declared_features": "[\"aes-gcm\", \"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"config-json5\", \"config-toml\", \"getrandom\", \"html-manipulation\", \"isolation\", \"json5\", \"proc-macro2\", \"process-relaunch-dangerous-allow-symlink-macos\", \"quote\", \"resources\", \"schema\", \"schemars\", \"serialize-to-javascript\", \"swift-rs\", \"walkdir\"]", "target": 7530130812022395703, "profile": 1369601567987815722, "path": 16521179712742780345, "deps": [[1200537532907108615, "url<PERSON><PERSON>n", false, 10228677288341506239], [3060637413840920116, "proc_macro2", false, 891366040531786399], [3129130049864710036, "memchr", false, 4694568426741312123], [3150220818285335163, "url", false, 2817011047027935690], [3191507132440681679, "serde_untagged", false, 14068641885811755087], [4899080583175475170, "semver", false, 479068139954186366], [5986029879202738730, "log", false, 8243596210684699674], [6213549728662707793, "serde_with", false, 9115110889762240363], [6606131838865521726, "ctor", false, 4837961135221239191], [6913375703034175521, "schemars", false, 5335601752749619569], [7170110829644101142, "json_patch", false, 13428550861027558761], [8319709847752024821, "uuid", false, 5835487573760639097], [9010263965687315507, "http", false, 13578335804324713031], [9451456094439810778, "regex", false, 1524823809369023226], [9556762810601084293, "brotli", false, 8359474311949613760], [9689903380558560274, "serde", false, 11826711578205325068], [10806645703491011684, "thiserror", false, 17237118037303288792], [11655476559277113544, "cargo_metadata", false, 16052753870913575053], [11989259058781683633, "dunce", false, 15620736502036581623], [13625485746686963219, "anyhow", false, 10552146487863275409], [14232843520438415263, "html5ever", false, 12926119041354842508], [15088007382495681292, "kuchiki", false, 16904528394391866150], [15367738274754116744, "serde_json", false, 9804563810918483774], [15609422047640926750, "toml", false, 5501034043401252101], [15622660310229662834, "walkdir", false, 15190750716767943960], [17146114186171651583, "infer", false, 382500881218984733], [17155886227862585100, "glob", false, 6894721913662188585], [17186037756130803222, "phf", false, 4982754992606688923], [17990358020177143287, "quote", false, 7170081911087925092]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-utils-5929d20b60a7055b\\dep-lib-tauri_utils", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}