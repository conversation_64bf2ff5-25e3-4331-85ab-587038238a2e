{"rustc": 16591470773350601817, "features": "[\"common-controls-v6\", \"compression\", \"custom-protocol\", \"default\", \"dynamic-acl\", \"tauri-runtime-wry\", \"webkit2gtk\", \"webview2-com\", \"wry\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"dynamic-acl\", \"http-range\", \"image\", \"image-ico\", \"image-png\", \"isolation\", \"linux-libxdo\", \"macos-private-api\", \"macos-proxy\", \"native-tls\", \"native-tls-vendored\", \"objc-exception\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-asset\", \"rustls-tls\", \"specta\", \"tauri-runtime-wry\", \"test\", \"tracing\", \"tray-icon\", \"unstable\", \"uuid\", \"webkit2gtk\", \"webview-data-url\", \"webview2-com\", \"wry\", \"x11\"]", "target": 12223948975794516716, "profile": 2040997289075261528, "path": 4259853515286349455, "deps": [[40386456601120721, "percent_encoding", false, 1790710389255581517], [1200537532907108615, "url<PERSON><PERSON>n", false, 5426483048459580631], [2013030631243296465, "webview2_com", false, 6288887377941338266], [2671782512663819132, "tauri_utils", false, 8049996397165116033], [3150220818285335163, "url", false, 144294874758154728], [3331586631144870129, "getrandom", false, 4951600733546308028], [4143744114649553716, "raw_window_handle", false, 6294644525462411982], [4494683389616423722, "muda", false, 7939492133785929086], [4919829919303820331, "serialize_to_javascript", false, 7649663438278564062], [5986029879202738730, "log", false, 5740330685498469161], [6089812615193535349, "tauri_runtime", false, 5151334907741498298], [7573826311589115053, "tauri_macros", false, 4425077654690390298], [9010263965687315507, "http", false, 17766510177696197715], [9538054652646069845, "tokio", false, 11280995406958655004], [9689903380558560274, "serde", false, 12580537829973814340], [10229185211513642314, "mime", false, 6748715409357346537], [10806645703491011684, "thiserror", false, 16536509331773121597], [11599800339996261026, "tauri_runtime_wry", false, 7752736397184301324], [11989259058781683633, "dunce", false, 3077557957928947652], [12565293087094287914, "window_vibrancy", false, 16977178105648259859], [12986574360607194341, "serde_repr", false, 5386973201235036973], [13077543566650298139, "heck", false, 7098945995636318566], [13116089016666501665, "windows", false, 8496220921591022370], [13625485746686963219, "anyhow", false, 7663442346581735029], [14039947826026167952, "build_script_build", false, 2776034111508375827], [15367738274754116744, "serde_json", false, 2971026785735191564], [16928111194414003569, "dirs", false, 14910650377135630083], [17155886227862585100, "glob", false, 6666768574698413362]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-06ca5ee8a619eee9\\dep-lib-tauri", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}