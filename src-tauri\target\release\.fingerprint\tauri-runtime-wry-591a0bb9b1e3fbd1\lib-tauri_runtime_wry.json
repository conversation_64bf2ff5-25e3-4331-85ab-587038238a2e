{"rustc": 16591470773350601817, "features": "[\"common-controls-v6\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"default\", \"devtools\", \"macos-private-api\", \"macos-proxy\", \"objc-exception\", \"tracing\", \"unstable\", \"x11\"]", "target": 1901661049345253480, "profile": 2040997289075261528, "path": 2795543797164131077, "deps": [[376837177317575824, "softbuffer", false, 14614000194877427971], [2013030631243296465, "webview2_com", false, 6288887377941338266], [2671782512663819132, "tauri_utils", false, 8049996397165116033], [3150220818285335163, "url", false, 144294874758154728], [3722963349756955755, "once_cell", false, 1054244042547292222], [4143744114649553716, "raw_window_handle", false, 6294644525462411982], [5986029879202738730, "log", false, 5740330685498469161], [6089812615193535349, "tauri_runtime", false, 5151334907741498298], [8826339825490770380, "tao", false, 9766282387451404534], [9010263965687315507, "http", false, 17766510177696197715], [9141053277961803901, "wry", false, 8352148263133361721], [11599800339996261026, "build_script_build", false, 3978240487635038985], [13116089016666501665, "windows", false, 8496220921591022370]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-runtime-wry-591a0bb9b1e3fbd1\\dep-lib-tauri_runtime_wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}