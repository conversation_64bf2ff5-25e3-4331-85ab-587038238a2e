{"rustc": 16591470773350601817, "features": "[\"default\", \"derive\", \"indexmap\", \"preserve_order\", \"schemars_derive\", \"url\", \"uuid1\"]", "declared_features": "[\"arrayvec\", \"arrayvec05\", \"arrayvec07\", \"bigdecimal\", \"bigdecimal03\", \"bigdecimal04\", \"bytes\", \"chrono\", \"default\", \"derive\", \"derive_json_schema\", \"either\", \"enumset\", \"impl_json_schema\", \"indexmap\", \"indexmap1\", \"indexmap2\", \"preserve_order\", \"raw_value\", \"rust_decimal\", \"schemars_derive\", \"semver\", \"smallvec\", \"smol_str\", \"ui_test\", \"url\", \"uuid\", \"uuid08\", \"uuid1\"]", "target": 11155677158530064643, "profile": 1369601567987815722, "path": 16232688688179812582, "deps": [[3150220818285335163, "url", false, 2817011047027935690], [6913375703034175521, "build_script_build", false, 4629496960259074782], [8319709847752024821, "uuid1", false, 5835487573760639097], [9122563107207267705, "dyn_clone", false, 2373401719641163257], [9689903380558560274, "serde", false, 11826711578205325068], [14923790796823607459, "indexmap", false, 9560662523582773071], [15367738274754116744, "serde_json", false, 9804563810918483774], [16071897500792579091, "schemars_derive", false, 6554462147121454126]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\schemars-3535a7ea4ecb5888\\dep-lib-schemars", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}