cargo:rerun-if-changed=permissions
cargo:PERMISSION_FILES_PATH=C:\Users\<USER>\Desktop\uclip\src-tauri\target\release\build\tauri-plugin-cli-fe2a4a22079d8494\out\tauri-plugin-cli-permission-files
cargo:rerun-if-env-changed=REMOVE_UNUSED_COMMANDS
cargo:GLOBAL_API_SCRIPT_PATH=\\?\C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tauri-plugin-cli-2.4.0\api-iife.js
cargo:rustc-check-cfg=cfg(mobile)
cargo:rustc-check-cfg=cfg(desktop)
cargo:rustc-cfg=desktop
