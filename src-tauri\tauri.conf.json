{"$schema": "https://schema.tauri.app/config/2", "productName": "uclip", "version": "0.1.0", "identifier": "com.uclip.app", "build": {"beforeDevCommand": "pnpm dev", "devUrl": "http://localhost:1450", "beforeBuildCommand": "pnpm build", "frontendDist": "../dist"}, "app": {"windows": [{"title": "uclip", "decorations": false, "minWidth": 1920, "minHeight": 1080}], "security": {"csp": null}}, "bundle": {"active": true, "targets": "all", "icon": ["icons/128x128.png", "icons/icon.ico"]}}