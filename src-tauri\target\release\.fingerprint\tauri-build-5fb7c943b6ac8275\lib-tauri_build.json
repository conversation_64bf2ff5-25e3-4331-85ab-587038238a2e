{"rustc": 16591470773350601817, "features": "[\"config-json\", \"default\"]", "declared_features": "[\"codegen\", \"config-json\", \"config-json5\", \"config-toml\", \"default\", \"isolation\", \"quote\", \"tauri-codegen\"]", "target": 1006236803848883740, "profile": 1369601567987815722, "path": 3256578575796693813, "deps": [[2671782512663819132, "tauri_utils", false, 2389448847489612802], [4899080583175475170, "semver", false, 479068139954186366], [6913375703034175521, "schemars", false, 5335601752749619569], [7170110829644101142, "json_patch", false, 13428550861027558761], [9689903380558560274, "serde", false, 11826711578205325068], [12714016054753183456, "tauri_winres", false, 76645245869424587], [13077543566650298139, "heck", false, 14315915692307211046], [13475171727366188400, "cargo_toml", false, 12812865218317247168], [13625485746686963219, "anyhow", false, 10552146487863275409], [15367738274754116744, "serde_json", false, 9804563810918483774], [15609422047640926750, "toml", false, 5501034043401252101], [15622660310229662834, "walkdir", false, 15190750716767943960], [16928111194414003569, "dirs", false, 15080384137259667900], [17155886227862585100, "glob", false, 6894721913662188585]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-build-5fb7c943b6ac8275\\dep-lib-tauri_build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}